import pyodbc
from neo4j import GraphDatabase
import time

# SQL Server connection configuration
SQL_SERVER = "127.0.0.1"  # Thay đổi nếu cần
SQL_DATABASES = ["EDU_NUCE", "EDU_NUCE_DATA", "HRM_NUCE"]
SQL_USER = "sa"  # Thay đổi username của bạn
SQL_PASSWORD = "123"  # Thay đổi password của bạn

# Hoặc sử dụng Windows Authentication (bỏ comment dòng dưới và comment 2 dòng trên)
# USE_WINDOWS_AUTH = True

# Neo4j connection configuration
NEO4J_URI = "neo4j://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "12345688"
NEO4J_DATABASE = "HUCE"

class TableDataChecker:
    def __init__(self):
        # Kết nối Neo4j
        try:
            self.neo4j_driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
            print("✅ Connected to Neo4j successfully")
        except Exception as e:
            raise RuntimeError(f"❌ Failed to connect to Neo4j: {e}")
    
    def close(self):
        try:
            self.neo4j_driver.close()
            print("🔒 Neo4j connection closed")
        except Exception as e:
            print(f"⚠️ Error closing Neo4j connection: {e}")
    
    def get_sql_connection(self, database):
        """Tạo kết nối đến SQL Server database"""
        try:
            # Kiểm tra có sử dụng Windows Authentication không
            if 'USE_WINDOWS_AUTH' in globals() and USE_WINDOWS_AUTH:
                connection_string = f"""
                DRIVER={{ODBC Driver 17 for SQL Server}};
                SERVER={SQL_SERVER};
                DATABASE={database};
                Trusted_Connection=yes;
                """
            else:
                connection_string = f"""
                DRIVER={{ODBC Driver 17 for SQL Server}};
                SERVER={SQL_SERVER},1433;
                DATABASE={database};
                UID={SQL_USER};
                PWD={SQL_PASSWORD};
                """

            conn = pyodbc.connect(connection_string)
            return conn
        except Exception as e:
            print(f"❌ Failed to connect to {database}: {e}")
            print(f"   Connection string: DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={SQL_SERVER};DATABASE={database};...")
            return None
    
    def get_tables_from_database(self, database):
        """Lấy danh sách tất cả bảng từ database"""
        conn = self.get_sql_connection(database)
        if not conn:
            return []
        
        try:
            cursor = conn.cursor()
            # Lấy danh sách bảng (không bao gồm views và system tables)
            cursor.execute("""
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE'
                ORDER BY TABLE_NAME
            """)
            tables = [row[0] for row in cursor.fetchall()]
            print(f"📊 Found {len(tables)} tables in {database}")
            return tables
        except Exception as e:
            print(f"❌ Error getting tables from {database}: {e}")
            return []
        finally:
            conn.close()
    
    def check_table_has_data(self, database, table_name):
        """Kiểm tra xem bảng có dữ liệu không"""
        conn = self.get_sql_connection(database)
        if not conn:
            return None
        
        try:
            cursor = conn.cursor()
            # Sử dụng TOP(1) để kiểm tra có dữ liệu không
            cursor.execute(f"SELECT TOP(1) * FROM [{table_name}]")
            row = cursor.fetchone()
            has_data = row is not None
            return has_data
        except Exception as e:
            print(f"⚠️ Error checking table {database}.{table_name}: {e}")
            return None
        finally:
            conn.close()
    
    def update_table_rate_in_neo4j(self, database, table_name, rate):
        """Cập nhật rate của bảng trong Neo4j"""
        try:
            with self.neo4j_driver.session(database=NEO4J_DATABASE) as session:
                # Tìm và cập nhật bảng
                result = session.run("""
                    MATCH (t:Table {name: $table_name, database: $database})
                    SET t.rate = $rate
                    RETURN t.name, t.database, t.rate
                """, table_name=table_name, database=database, rate=rate)
                
                updated = list(result)
                if updated:
                    print(f"✅ Updated {database}.{table_name}: rate = {rate}")
                    return True
                else:
                    print(f"⚠️ Table {database}.{table_name} not found in Neo4j")
                    return False
        except Exception as e:
            print(f"❌ Error updating Neo4j for {database}.{table_name}: {e}")
            return False
    
    def process_all_databases(self):
        """Xử lý tất cả databases"""
        total_tables = 0
        updated_tables = 0
        
        for database in SQL_DATABASES:
            print(f"\n{'='*60}")
            print(f"🔍 Processing database: {database}")
            print('='*60)
            
            # Lấy danh sách bảng
            tables = self.get_tables_from_database(database)
            total_tables += len(tables)
            
            for i, table_name in enumerate(tables, 1):
                print(f"\n📋 [{i}/{len(tables)}] Checking {database}.{table_name}...")
                
                # Kiểm tra có dữ liệu không
                has_data = self.check_table_has_data(database, table_name)
                
                if has_data is None:
                    print(f"❌ Could not check {table_name}")
                    continue
                
                # Xác định rate
                rate = 0 if has_data else -1
                status = "HAS DATA" if has_data else "NO DATA"
                print(f"   📊 {status} → rate = {rate}")
                
                # Cập nhật Neo4j
                if self.update_table_rate_in_neo4j(database, table_name, rate):
                    updated_tables += 1
                
                # Nghỉ một chút để không quá tải
                time.sleep(0.1)
        
        print(f"\n{'='*60}")
        print(f"📊 SUMMARY")
        print('='*60)
        print(f"Total tables processed: {total_tables}")
        print(f"Tables updated in Neo4j: {updated_tables}")
        print(f"Success rate: {updated_tables/total_tables*100:.1f}%" if total_tables > 0 else "No tables processed")

def main():
    print("🚀 Starting table data check process...")
    print(f"📋 Databases to check: {', '.join(SQL_DATABASES)}")
    
    checker = TableDataChecker()
    
    try:
        checker.process_all_databases()
    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    finally:
        checker.close()
    
    print("\n✅ Process completed!")

if __name__ == "__main__":
    main()
