from neo4j import GraphDatabase
import numpy as np
from sentence_transformers import SentenceTransformer
import re

# Neo4j connection configuration
NEO4J_URI = "neo4j://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "12345688"
NEO4J_DATABASE = "HUCE"
EMBEDDING_MODEL = 'all-MiniLM-L6-v2'

# Load sentence transformer model
MODEL = SentenceTransformer(EMBEDDING_MODEL)

class Neo4jSmartVectorSearch:
    def __init__(self):
        try:
            self.driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
            print("✅ Connected to Neo4j successfully")
        except Exception as e:
            raise RuntimeError(f"❌ Failed to connect to Neo4j: {e}")

    def close(self):
        try:
            self.driver.close()
            print("🔒 Neo4j connection closed")
        except Exception as e:
            print(f"⚠️ Error closing Neo4j connection: {e}")

    def check_schema(self):
        try:
            with self.driver.session(database=NEO4J_DATABASE) as session:
                labels = [record["label"] for record in session.run("CALL db.labels()")]
                props = [(r["labels(n)"], r["keys(n)"]) for r in session.run("MATCH (n) RETURN labels(n), keys(n) LIMIT 10")]
                return labels, props
        except Exception as e:
            print(f"⚠️ Schema check failed: {e}")
            return [], []

    def update_description_vectors(self):
        query = """
        MATCH (t:Table{database:'EDU_NUCE'})
        RETURN t.name AS table_name, t.description AS description
        """
        with self.driver.session(database=NEO4J_DATABASE) as session:
            result = session.run(query)
            for record in result:
                name, desc = record["table_name"], record["description"]
                if not desc:
                    continue
                enriched_desc = self._enrich_description(name, desc)
                vec = MODEL.encode(enriched_desc).astype(np.float32).tolist()
                session.run("""
                    MATCH (t:Table {name: $table_name, database: 'EDU_NUCE'})
                    SET t.description_vector = $vector
                """, table_name=name, vector=vec)
                print(f"✅ Updated vector for table: {name}")

    def _enrich_description(self, name, desc):
        # Thêm nhãn đánh dấu bảng chính dựa vào tên
        if re.search(r'sinhvien|student|DT_SinhVien', name, re.IGNORECASE):
            return f"[PRIMARY ENTITY] {desc}"
        return desc

    def create_vector_index(self):
        try:
            with self.driver.session(database=NEO4J_DATABASE) as session:
                session.run("""
                    CREATE VECTOR INDEX table_description_index IF NOT EXISTS
                    FOR (t:Table) ON (t.description_vector)
                    OPTIONS {indexConfig: {`vector.dimensions`: 384, `vector.similarity_function`: "cosine"}}
                """)
                print("📌 Created vector index on table descriptions")
        except Exception as e:
            print(f"⚠️ Vector index creation failed: {e}")

    def search_relevant_table(self, question, top_k=30):
        question_vector = MODEL.encode(question).astype(np.float32).tolist()
        query = """
        CALL db.index.vector.queryNodes('table_description_index', $top_k, $question_vector)
        YIELD node, score
        RETURN node.name AS table_name, node.description AS description, score
        ORDER BY score DESC
        """
        with self.driver.session(database=NEO4J_DATABASE) as session:
            try:
                result = session.run(query, top_k=top_k, question_vector=question_vector)
                return [(r["table_name"], r["description"], r["score"]) for r in result]
            except Exception as e:
                print(f"❌ Vector search failed: {e}")
                return []

def main():
    question = "Họ và tên sinh viên có mã là 85365"
    searcher = Neo4jSmartVectorSearch()

    try:
        labels, props = searcher.check_schema()
        if "Table" not in labels:
            print("❌ 'Table' label not found in Neo4j")
            return

        searcher.update_description_vectors()
        searcher.create_vector_index()

        print(f"\n🔍 Searching for relevant table for question: '{question}'")
        results = searcher.search_relevant_table(question)
        for table, desc, score in results:
            print(f"\n📊 Table: {table}\n📄 Description: {desc}\n⭐ Score: {score:.4f}")
        if not results:
            print("⚠️ No relevant tables found")
    finally:
        searcher.close()

if __name__ == "__main__":
    main()