from neo4j import GraphDatabase
import numpy as np
from sentence_transformers import SentenceTransformer

# C<PERSON>u hình kết nối Neo4j
NEO4J_URI = "neo4j://127.0.0.1:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "12345688"
NEO4J_DATABASE = "HUCE"

# Khởi tạo model embedding
MODEL = SentenceTransformer('all-MiniLM-L6-v2')

class Neo4jVectorTest:
    def __init__(self):
        self.driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))

    def close(self):
        self.driver.close()

    def search(self, question, top_k=2):
        question_vector = MODEL.encode(question).astype(np.float32).tolist()
        query = """
        CALL db.index.vector.queryNodes('table_description_index', $top_k, $question_vector)
        YIELD node, score
        RETURN node.name AS table_name, node.description AS description, score
        ORDER BY score DESC
        """
        with self.driver.session(database=NEO4J_DATABASE) as session:
            result = session.run(query, top_k=top_k, question_vector=question_vector)
            print(f"\nKết quả tìm kiếm cho câu hỏi: '{question}'")
            for record in result:
                print(f"Table: {record['table_name']}, Description: {record['description']}, Similarity Score: {record['score']}")

def main():
    tester = Neo4jVectorTest()
    tester.search("Họ và tên sinh viên có mã số sinh viên là 85365", top_k=10)
    tester.close()

if __name__ == "__main__":
    main() 