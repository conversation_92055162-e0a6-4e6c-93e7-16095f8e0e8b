from neo4j import GraphDatabase
import numpy as np
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import unicodedata

# Thử import các thư viện embedding
try:
    from gensim.models import Word2Vec, KeyedVectors
    GENSIM_AVAILABLE = True
    print("✅ Gensim available")
except ImportError:
    GENSIM_AVAILABLE = False
    print("❌ Gensim not available")

try:
    from transformers import AutoModel, AutoTokenizer
    import torch
    TRANSFORMERS_AVAILABLE = True
    print("✅ Transformers available")
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("❌ Transformers not available")

# Neo4j connection configuration
NEO4J_URI = "neo4j://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "12345688"
NEO4J_DATABASE = "HUCE"

# Vietnamese-optimized embedding class
class VietnameseEmbedding:
    def __init__(self):
        self.vectorizer = TfidfVectorizer(
            max_features=512,
            stop_words=None,
            ngram_range=(1, 3),  # Unigram, bigram, trigram
            lowercase=True,
            analyzer='char_wb',  # Character n-grams tốt cho tiếng Việt
            min_df=1,
            max_df=0.95
        )
        self.is_fitted = False

        # Vietnamese-specific keywords và synonyms
        self.vietnamese_synonyms = {
            'sinh viên': ['sinh vien', 'hoc sinh', 'hoc vien', 'student'],
            'giảng viên': ['giang vien', 'thay co', 'giao vien', 'teacher'],
            'môn học': ['mon hoc', 'hoc phan', 'subject'],
            'điểm': ['diem', 'diem so', 'ket qua', 'score', 'grade'],
            'họ tên': ['ho ten', 'ten', 'name', 'fullname'],
            'mã số': ['ma so', 'ma', 'id', 'code'],
            'thông tin': ['thong tin', 'du lieu', 'info', 'information'],
            'danh sách': ['danh sach', 'list'],
            'tìm kiếm': ['tim kiem', 'search', 'find']
        }

    def normalize_vietnamese(self, text):
        """Chuẩn hóa và mở rộng text tiếng Việt"""
        if not text:
            return ""

        text = str(text).lower().strip()

        # Thêm synonyms
        expanded_text = text
        for main_word, synonyms in self.vietnamese_synonyms.items():
            if main_word in text:
                expanded_text += " " + " ".join(synonyms)

        return expanded_text

    def fit(self, texts):
        """Fit vectorizer với corpus"""
        processed_texts = [self.normalize_vietnamese(str(text)) for text in texts if text]
        if processed_texts:
            self.vectorizer.fit(processed_texts)
            self.is_fitted = True
            print(f"📚 Fitted Vietnamese vectorizer with {len(processed_texts)} texts")

    def encode(self, text):
        """Encode text thành vector"""
        processed = self.normalize_vietnamese(str(text))

        if not self.is_fitted:
            # Nếu chưa fit, tạo vectorizer đơn giản
            temp_vectorizer = TfidfVectorizer(
                max_features=512,
                ngram_range=(1, 3),
                analyzer='char_wb',
                min_df=1
            )
            vector = temp_vectorizer.fit_transform([processed]).toarray()[0]
        else:
            vector = self.vectorizer.transform([processed]).toarray()[0]

        return vector.astype(np.float32)

# Simple TF-IDF based embedding class (fallback)
class SimpleEmbedding:
    def __init__(self):
        self.vectorizer = TfidfVectorizer(
            max_features=768,  # Tăng lên để match PhoBERT
            stop_words=None,   # Không remove stop words cho tiếng Việt
            ngram_range=(1, 3),  # Unigram, bigram, trigram
            lowercase=True,
            analyzer='char_wb',  # Character n-grams tốt hơn cho tiếng Việt
            min_df=1
        )
        self.is_fitted = False

    def normalize_vietnamese(self, text):
        """Chuẩn hóa text tiếng Việt"""
        if not text:
            return ""
        return str(text).lower().strip()

    def fit(self, texts):
        """Fit vectorizer với corpus"""
        processed_texts = [self.normalize_vietnamese(str(text)) for text in texts if text]
        if processed_texts:
            self.vectorizer.fit(processed_texts)
            self.is_fitted = True

    def encode(self, text):
        """Encode text thành vector"""
        if not self.is_fitted:
            # Nếu chưa fit, tạo vectorizer đơn giản
            processed = self.normalize_vietnamese(str(text))
            temp_vectorizer = TfidfVectorizer(max_features=768, ngram_range=(1, 3),
                                            analyzer='char_wb', min_df=1)
            vector = temp_vectorizer.fit_transform([processed]).toarray()[0]
        else:
            processed = self.normalize_vietnamese(str(text))
            vector = self.vectorizer.transform([processed]).toarray()[0]

        return vector.astype(np.float32)

# Chọn model phù hợp cho tiếng Việt
print("🇻🇳 Using Vietnamese-optimized TF-IDF Embedding")
MODEL = VietnameseEmbedding()

class Neo4jSmartVectorSearch:
    def __init__(self):
        try:
            self.driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
            print("✅ Connected to Neo4j successfully")
        except Exception as e:
            raise RuntimeError(f"❌ Failed to connect to Neo4j: {e}")

    def close(self):
        try:
            self.driver.close()
            print("🔒 Neo4j connection closed")
        except Exception as e:
            print(f"⚠️ Error closing Neo4j connection: {e}")

    def check_schema(self):
        try:
            with self.driver.session(database=NEO4J_DATABASE) as session:
                labels = [record["label"] for record in session.run("CALL db.labels()")]
                props = [(r["labels(n)"], r["keys(n)"]) for r in session.run("MATCH (n) RETURN labels(n), keys(n) LIMIT 10")]
                return labels, props
        except Exception as e:
            print(f"⚠️ Schema check failed: {e}")
            return [], []

    def update_description_vectors(self):
        # Lấy chỉ những bảng có rate = 0 (có dữ liệu)
        query = """
        MATCH (t:Table{database:'EDU_NUCE'})
        WHERE t.rate = 0
        RETURN t.name AS table_name, t.description AS description
        """
        with self.driver.session(database=NEO4J_DATABASE) as session:
            result = session.run(query)
            records = list(result)
            
            # Fit vectorizer với tất cả descriptions
            descriptions = [self._enrich_description(r["table_name"], r["description"]) 
                          for r in records if r["description"]]
            if descriptions:
                MODEL.fit(descriptions)
                print(f"📚 Fitted vectorizer with {len(descriptions)} descriptions")
            
            # Tạo vectors cho từng bảng
            for record in records:
                name, desc = record["table_name"], record["description"]
                if not desc:
                    continue
                enriched_desc = self._enrich_description(name, desc)
                vec = MODEL.encode(enriched_desc).tolist()
                session.run("""
                    MATCH (t:Table {name: $table_name, database: 'EDU_NUCE'})
                    SET t.description_vector = $vector
                """, table_name=name, vector=vec)
                print(f"✅ Updated vector for table: {name}")

    def _enrich_description(self, name, desc):
        # Thêm nhiều từ khóa cho bảng sinh viên
        if re.search(r'sinhvien|student|DT_SinhVien', name, re.IGNORECASE):
            return f"[SINH VIEN] {desc} thong tin ca nhan ho ten ma sinh vien"
        return desc

    def create_vector_index(self):
        try:
            # Lấy dimensions từ model hiện tại
            test_vector = MODEL.encode("test")
            dimensions = len(test_vector)
            print(f"🔢 Model dimensions: {dimensions}")
            
            with self.driver.session(database=NEO4J_DATABASE) as session:
                # Drop index cũ nếu có
                try:
                    session.run("DROP INDEX table_description_index IF EXISTS")
                except:
                    pass
                    
                # Tạo index mới với dimensions đúng
                session.run(f"""
                    CREATE VECTOR INDEX table_description_index IF NOT EXISTS
                    FOR (t:Table) ON (t.description_vector)
                    OPTIONS {{indexConfig: {{`vector.dimensions`: {dimensions}, `vector.similarity_function`: "cosine"}}}}
                """)
                print(f"📌 Created vector index with {dimensions} dimensions")
        except Exception as e:
            print(f"⚠️ Vector index creation failed: {e}")

    def search_relevant_table(self, question, top_k=30):
        # Chỉ dùng Vector search thuần túy (chỉ bảng có rate = 0)
        question_vector = MODEL.encode(question).tolist()
        query = """
        CALL db.index.vector.queryNodes('table_description_index', $top_k, $question_vector)
        YIELD node, score
        WHERE node.rate = 0
        RETURN node.name AS table_name, node.description AS description, score
        ORDER BY score DESC
        """
        with self.driver.session(database=NEO4J_DATABASE) as session:
            try:
                vector_results = session.run(query, top_k=top_k, question_vector=question_vector)
                vector_results = [(r["table_name"], r["description"], r["score"]) for r in vector_results]

                print(f"\n🔍 Pure Vector search results (top 10):")
                for i, (table, desc, score) in enumerate(vector_results[:10]):
                    print(f"   {i+1}. {table}: {score:.4f}")

                # Tìm DT_SinhVien trong vector results
                dt_sinhvien_position = None
                dt_sinhvien_vector_score = None
                for i, (table, desc, score) in enumerate(vector_results):
                    if table == 'DT_SinhVien':
                        dt_sinhvien_vector_score = score
                        dt_sinhvien_position = i + 1
                        break

                if dt_sinhvien_vector_score is not None:
                    print(f"\n🎯 DT_SinhVien:")
                    print(f"   Position: #{dt_sinhvien_position}")
                    print(f"   Vector score: {dt_sinhvien_vector_score:.4f}")
                else:
                    print(f"\n❌ DT_SinhVien not found in top {top_k} results")

                return vector_results
            except Exception as e:
                print(f"❌ Vector search failed: {e}")
                return []

    def _keyword_search(self, question):
        """Tìm kiếm dựa trên từ khóa trực tiếp (chỉ bảng có rate = 0)"""
        keywords_map = {
            'sinh viên': ['DT_SinhVien', 'DT_HoSoSinhVien'],
            'giảng viên': ['DT_GiangVien'],
            'môn học': ['DT_MonHoc'],
            'điểm': ['DT_Diem', 'DT_KetQuaHocTap'],
            'lớp': ['DT_Lop'],
            'khoa': ['DT_Khoa']
        }

        question_lower = question.lower()
        matched_tables = []

        # Kiểm tra từng keyword
        for keyword, tables in keywords_map.items():
            if keyword in question_lower:
                # Kiểm tra bảng nào có rate = 0 trong Neo4j
                with self.driver.session(database=NEO4J_DATABASE) as session:
                    for table in tables:
                        result = session.run("""
                            MATCH (t:Table {name: $table_name, database: 'EDU_NUCE'})
                            WHERE t.rate = 0
                            RETURN t.name
                        """, table_name=table)

                        if list(result):  # Nếu bảng tồn tại và có rate = 0
                            matched_tables.append((table, f"Keyword match: {keyword}", 1.0))

        return matched_tables
    
    def _combine_results(self, keyword_results, vector_results):
        """Kết hợp kết quả keyword và vector, ưu tiên keyword"""
        combined = {}

        # Add keyword results với boost score
        for table, desc, score in keyword_results:
            boosted_score = score + 0.3  # Boost keyword matches
            combined[table] = (table, desc, boosted_score)
            print(f"🔑 Keyword boost: {table} = {score:.4f} + 0.3 = {boosted_score:.4f}")

        # Add vector results
        for table, desc, score in vector_results:
            if table not in combined:
                combined[table] = (table, desc, score)
            else:
                # Nếu đã có từ keyword, giữ score cao hơn
                existing_score = combined[table][2]
                final_score = max(existing_score, score)
                combined[table] = (table, desc, final_score)
                if table == 'DT_SinhVien':
                    print(f"🎯 DT_SinhVien final: max({existing_score:.4f}, {score:.4f}) = {final_score:.4f}")

        # Sort by score descending
        return sorted(combined.values(), key=lambda x: x[2], reverse=True)

def test_multiple_questions():
    """Test nhiều câu hỏi khác nhau để xem sự thay đổi ranking"""
    questions = [
        "Tổng hợp những sinh viên có tên là Hoàng",
        "Họ tên sinh viên có mã số 85365",
        "Thông tin sinh viên",
        "Danh sách học sinh",
        "Tìm kiếm thông tin cá nhân sinh viên",
        "Sinh viên có điểm cao"
    ]

    searcher = Neo4jSmartVectorSearch()

    try:
        # Setup một lần
        labels, props = searcher.check_schema()
        if "Table" not in labels:
            print("❌ 'Table' label not found in Neo4j")
            return

        searcher.update_description_vectors()
        searcher.create_vector_index()

        # Test từng câu hỏi
        for i, question in enumerate(questions):
            print(f"\n{'='*60}")
            print(f"🔍 TEST {i+1}: '{question}'")
            print('='*60)

            results = searcher.search_relevant_table(question, top_k=10)

            # Tìm vị trí của DT_SinhVien
            dt_sinhvien_position = None
            dt_sinhvien_score = None
            for pos, (table, desc, score) in enumerate(results):
                if table == 'DT_SinhVien':
                    dt_sinhvien_position = pos + 1
                    dt_sinhvien_score = score
                    break

            if dt_sinhvien_position:
                print(f"🎯 DT_SinhVien: Position #{dt_sinhvien_position}, Score: {dt_sinhvien_score:.4f}")
            else:
                print(f"❌ DT_SinhVien: Not in top 10")

            # Show top 3 để so sánh
            print(f"\n📊 Top 3 results:")
            for j, (table, desc, score) in enumerate(results[:3]):
                print(f"   {j+1}. {table}: {score:.4f}")

    finally:
        searcher.close()

def main():
    # Chạy test multiple questions
    test_multiple_questions()

if __name__ == "__main__":
    main()
